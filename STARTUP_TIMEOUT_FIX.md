# VM Startup Timeout Fix & Resource Cleanup

## 🚨 **IMMEDIATE ACTION REQUIRED**

### **Current Situation**:
- ✅ VM created successfully: `data-pipeline-dev-dev-pipeline-vm`
- ❌ Startup script timeout after 10 minutes
- 💰 **Resources still running and costing money!**

### **STEP 1: Destroy Resources NOW**

**Go to GitHub Actions immediately and run**:
1. **Click "Run Data Pipeline"**
2. **Select**: Environment: `dev`, Action: `destroy-existing`
3. **Click "Run workflow"**

This will destroy:
- VM: `data-pipeline-dev-dev-pipeline-vm`
- Storage: `***-pipeline-data-7f859671`
- Network: `data-pipeline-dev-dev-network`
- All firewall rules and subnets

## 🔍 **Issues Found & Fixed**

### **Issue 1: Double Environment Naming**
**Problem**: `data-pipeline-dev-dev-pipeline-vm` (dev appears twice)
**Cause**: Environment variable duplication in terraform.tfvars
**Fix**: ✅ Changed `project_name = "data-pipeline-$env"` to `project_name = "data-pipeline"`

### **Issue 2: Startup Script Timeout**
**Problem**: 10 minutes not enough for complete setup
**Cause**: SSH connection test + package installation + database setup
**Fix**: ✅ Increased timeout to 15 minutes with progress logging

### **Issue 3: No Debug Information**
**Problem**: No visibility into what's causing the delay
**Fix**: ✅ Added partial log output every 5 attempts

## ✅ **Fixes Applied**

### **1. Fixed Resource Naming**:
```yaml
# BEFORE:
project_name = "data-pipeline-dev"  # Creates data-pipeline-dev-dev-vm

# AFTER:
project_name = "data-pipeline"      # Creates data-pipeline-dev-vm
```

### **2. Increased Timeout**:
```yaml
# BEFORE:
for i in {1..60}; do  # 10 minutes

# AFTER:
for i in {1..90}; do  # 15 minutes
```

### **3. Added Debug Logging**:
```yaml
# Show partial logs every 5 attempts
if [ $((i % 5)) -eq 0 ]; then
  echo "=== Partial startup logs ==="
  gcloud compute instances get-serial-port-output $VM_NAME | tail -10
fi
```

## 🎯 **Expected Results After Fix**

### **Correct Resource Names**:
- VM: `data-pipeline-dev-pipeline-vm` ✅
- Network: `data-pipeline-dev-network` ✅
- Bucket: `project-pipeline-data-xxxxx` ✅

### **Startup Process**:
```
Checking startup progress... (attempt 1/90)
Checking startup progress... (attempt 5/90)
=== Partial startup logs (attempt 5) ===
[Last 10 lines of startup script output]
...
VM startup completed successfully!
```

### **Complete Pipeline Flow**:
```
✅ VM Creation: 2-3 minutes
✅ Startup Script: 10-15 minutes (includes SSH test)
✅ Pipeline Execution: 5-10 minutes
✅ Automatic Cleanup: 2-3 minutes
Total: ~20-30 minutes
```

## 🚀 **Next Steps**

### **1. Destroy Current Resources** (Do this first!)
```
GitHub Actions → Run Data Pipeline → destroy-existing
```

### **2. Push the Fixes**:
```bash
git add .
git commit -m "Fix environment naming and increase startup timeout"
git push origin main
```

### **3. Test the Fixed Pipeline**:
1. **Wait for destroy to complete**
2. **Run Data Pipeline** again
3. **Select**: Environment: `dev`, Action: `test-ssh-only`
4. **Monitor**: Should complete in 15 minutes with SSH results

### **4. Verify Success Indicators**:
- ✅ VM name: `data-pipeline-dev-pipeline-vm` (no double-dev)
- ✅ Startup completes within 15 minutes
- ✅ SSH connection test results visible
- ✅ Automatic cleanup works

## 🔧 **Troubleshooting**

### **If Startup Still Times Out**:
1. **Check AWS EC2**: Ensure it's running and accessible
2. **Check Security Groups**: Allow SSH from GCP IP ranges
3. **Check SSH Keys**: Verify they're correct in GitHub secrets
4. **Manual Debug**: SSH to VM manually to check startup script

### **If SSH Connection Fails**:
1. **AWS Security Groups**: Must allow port 22 from 0.0.0.0/0 or GCP ranges
2. **AWS EC2 Status**: Instance must be running
3. **SSH Keys**: Must match exactly (including newlines)
4. **AWS User**: Usually `ubuntu`, `ec2-user`, or `forge`

### **Manual VM Cleanup** (if destroy workflow fails):
1. **GCP Console** → **Compute Engine** → **VM instances**
2. **Select** `data-pipeline-dev-dev-pipeline-vm`
3. **Delete** (this will also clean up disks)
4. **VPC network** → **Delete** `data-pipeline-dev-dev-network`
5. **Cloud Storage** → **Delete** bucket `***-pipeline-data-7f859671`

## 💰 **Cost Impact**

### **Current Cost** (while resources run):
- VM `e2-standard-4`: ~$0.15/hour
- Storage: ~$0.01/hour
- Network: Minimal
- **Total**: ~$3.60/day if left running

### **After Fix**:
- Pipeline run: ~$0.50 for 30 minutes
- Automatic cleanup: $0 ongoing cost
- **Total**: <$1 per pipeline execution

## ⚠️ **Important Notes**

1. **Always destroy resources** after testing
2. **Monitor GCP billing** for unexpected charges
3. **Use `test-ssh-only`** for connection testing
4. **Use `run-with-cleanup`** for production runs
5. **Check startup logs** if timeouts occur

## 📋 **Success Checklist**

- [ ] **Destroy current resources** (immediate)
- [ ] **Push naming and timeout fixes**
- [ ] **Test with `test-ssh-only`**
- [ ] **Verify SSH connection works**
- [ ] **Confirm automatic cleanup**
- [ ] **Ready for production use**

**PRIORITY: Destroy the current resources first to stop costs, then apply the fixes!**
