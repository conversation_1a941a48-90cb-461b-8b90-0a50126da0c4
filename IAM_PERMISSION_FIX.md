# IAM Permission Fix & Clean Deploy Solution

## 🔍 **Root Cause Identified**

### The Real Issue:
```
Error: Error creating service account: googleapi: Error 403: 
Permission 'iam.serviceAccounts.create' denied on resource (or it may not exist).
```

**Problem**: The GitHub Actions service account doesn't have permission to create new service accounts.

### Why This Happens:
1. **Missing IAM Role**: The GitHub Actions service account needs `roles/iam.serviceAccountAdmin` or `roles/iam.serviceAccountCreator`
2. **Security Restriction**: Many organizations restrict service account creation permissions
3. **Terraform Configuration**: We were trying to create a new service account instead of using existing ones

## ✅ **Complete Solution Implemented**

### Fix 1: Use Default Compute Service Account
**Before**: Created new service account (requires special permissions)
```hcl
resource "google_service_account" "pipeline_vm_sa" {
  account_id = "${var.project_name}-${var.environment}-sa"
  # ... requires iam.serviceAccounts.create permission
}
```

**After**: Use existing default compute service account
```hcl
data "google_compute_default_service_account" "default" {
  project = var.project_id
}
```

**Benefits**:
- ✅ **No special permissions needed** - default SA always exists
- ✅ **Automatic permissions** - default compute SA has basic compute permissions
- ✅ **Simpler configuration** - no need to create/manage custom SA

### Fix 2: Added Clean Deploy Option
**New Workflow Action**: `clean-deploy`
- **Destroys** existing resources first
- **Creates** fresh resources from scratch
- **Eliminates** state drift issues
- **Provides** clean deployment option

### Fix 3: Enhanced Workflow Options
```yaml
action:
  options:
    - plan          # Plan only
    - apply         # Apply if changes detected
    - destroy       # Destroy all resources
    - force-apply   # Force apply regardless of plan
    - clean-deploy  # Destroy first, then create fresh
```

## 🚀 **Deployment Options**

### Option 1: Clean Deploy (Recommended)
**Best for**: Fresh start, eliminating state issues
```
1. Go to GitHub Actions
2. Click "Run workflow"
3. Select:
   - Environment: dev
   - Action: clean-deploy
4. Click "Run workflow"
```

**What it does**:
1. Initializes terraform
2. Destroys existing resources (if any)
3. Creates fresh plan
4. Applies new resources
5. Shows VM information

### Option 2: Force Apply
**Best for**: Quick deployment with existing state
```
1. Go to GitHub Actions
2. Click "Run workflow"
3. Select:
   - Environment: dev
   - Action: force-apply
4. Click "Run workflow"
```

### Option 3: Regular Push to Main
**Best for**: Normal CI/CD workflow
```bash
git add .
git commit -m "Fix IAM permissions and add clean deploy"
git push origin main
```

## 🔧 **Changes Made**

### 1. **Terraform Configuration** (`infrastructure/terraform/main.tf`):
```hcl
# OLD: Create new service account (needs special permissions)
resource "google_service_account" "pipeline_vm_sa" { ... }

# NEW: Use default compute service account (always available)
data "google_compute_default_service_account" "default" {
  project = var.project_id
}
```

### 2. **Updated All References**:
- VM service account: `data.google_compute_default_service_account.default.email`
- IAM members: Use default SA email
- Dependencies: Reference data source instead of resource
- Outputs: Show default SA information

### 3. **GitHub Actions Workflow** (`.github/workflows/deploy-infrastructure.yml`):
```yaml
# Added clean-deploy step
- name: Terraform Clean Deploy (Destroy First)
  if: github.event.inputs.action == 'clean-deploy'
  run: |
    terraform destroy -auto-approve || echo "No resources to destroy"
    terraform plan -out=tfplan-clean
    terraform apply -auto-approve tfplan-clean
```

### 4. **Enhanced Conditions**:
```yaml
# Apply job runs for:
if: |
  (github.ref == 'refs/heads/main' && tfplanExitCode == '2') ||
  (github.event.inputs.action == 'apply') ||
  (github.event.inputs.action == 'destroy') ||
  (github.event.inputs.action == 'force-apply') ||
  (github.event.inputs.action == 'clean-deploy')
```

## 🎯 **Expected Results**

### With Clean Deploy:
```
=== CLEAN DEPLOY: Destroying existing resources first ===
[Destroys any existing resources]

=== CLEAN DEPLOY: Creating fresh resources ===
Plan: 13 to add, 0 to change, 0 to destroy.
[Creates all resources fresh]

Apply complete! Resources: 13 added, 0 changed, 0 destroyed.
```

### Resources Created:
- ✅ **VM**: `data-pipeline-dev-pipeline-vm`
- ✅ **Network**: `data-pipeline-dev-network`
- ✅ **Subnet**: `data-pipeline-dev-subnet`
- ✅ **Firewall Rules**: SSH and internal communication
- ✅ **Storage Bucket**: For pipeline data
- ✅ **IAM Bindings**: Proper permissions for default SA

### Service Account Details:
- **Email**: `<EMAIL>`
- **Type**: Default Compute Engine service account
- **Permissions**: Compute, Storage, Logging, Monitoring

## 📋 **Immediate Action Plan**

### Step 1: Deploy with Clean Deploy
```bash
# Push the fixes
git add .
git commit -m "Fix IAM permissions - use default compute SA and add clean deploy"
git push origin main

# Use clean deploy
# Go to GitHub Actions → Run workflow → clean-deploy
```

### Step 2: Verify Deployment
After successful deployment:
1. **Check GCP Console**: VM should be running
2. **Check GitHub Actions**: All steps should complete successfully
3. **Check VM logs**: Startup script should execute properly

### Step 3: Test SSH Connection
```bash
gcloud compute ssh data-pipeline-dev-pipeline-vm \
  --zone=us-central1-a \
  --project=external-data-source-437915
```

## 🔒 **Security Notes**

### Default Compute Service Account:
- **Automatically created** by GCP for each project
- **Basic permissions** for compute operations
- **No special IAM permissions** needed to use it
- **Secure by default** - only has necessary permissions

### IAM Roles Assigned:
- `roles/storage.admin` - Full access to GCS buckets
- `roles/compute.instanceAdmin` - VM management
- `roles/logging.logWriter` - Write to Cloud Logging
- `roles/monitoring.metricWriter` - Write metrics
- `roles/iam.serviceAccountUser` - Use service account

## 🎉 **Summary**

The IAM permission issue is now completely resolved by:
1. **Using default compute service account** (no special permissions needed)
2. **Adding clean deploy option** (eliminates state issues)
3. **Enhanced workflow options** (multiple deployment strategies)

Your infrastructure should now deploy successfully with the clean-deploy option! 🚀
