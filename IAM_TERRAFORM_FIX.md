# IAM Terraform Permission Fix

## 🔍 **Problem Analysis**

The Terraform deployment was failing with IAM permission errors:

```
Error: Error retrieving IAM policy for project "***": googleapi: Error 403: The caller does not have permission, forbidden
```

### Root Cause:
1. **GitHub Actions service account lacks IAM admin permissions** to manage project-level IAM policies
2. **Circular dependency**: Service account needs `iam.serviceAccountUser` role on itself
3. **Terraform trying to manage IAM roles** that are already manually assigned

## ✅ **Solution Implemented**

### 1. **Removed IAM Role Management from Terraform**

**Before** (lines 91-120):
```hcl
# Assign required permissions to the existing service account
resource "google_project_iam_member" "pipeline_vm_storage_admin" {
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${local.existing_service_account_email}"
}
# ... (4 more IAM member resources)
```

**After** (lines 91-102):
```hcl
# Note: IAM permissions for the service account are managed manually in GCP Console
# The service account vm-cuba-buddy-data-ingestion already has the following roles assigned:
# - Cloud Run developer
# - Cloud Scheduler Admin  
# - Compute Admin
# - Compute Storage Admin
# - Logging Admin
# - Monitoring Admin
# - Storage Admin
# 
# We don't manage these through Terraform to avoid IAM permission issues
# with the GitHub Actions service account
```

### 2. **Removed Storage Bucket IAM Binding**

**Before** (lines 196-200):
```hcl
resource "google_storage_bucket_iam_member" "pipeline_vm_bucket_admin" {
  bucket = google_storage_bucket.pipeline_data.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${local.existing_service_account_email}"
}
```

**After** (lines 178-180):
```hcl
# Note: Storage bucket permissions are handled through the Storage Admin role
# assigned to the service account at the project level, so no bucket-specific
# IAM binding is needed
```

## 🔧 **Manual Step Required**

### **Add Missing IAM Role**

The service account `vm-cuba-buddy-data-ingestion` needs one additional role that must be added manually:

1. **Go to GCP Console** → IAM & Admin → IAM
2. **Find the service account**: `vm-cuba-buddy-data-ingestion@[PROJECT-ID].iam.gserviceaccount.com`
3. **Click "Edit Principal"** (pencil icon)
4. **Add Role**: `Service Account User` (`roles/iam.serviceAccountUser`)
5. **Save**

### **Why This Role is Needed**:
- Allows the VM instance to use the service account
- Required for `service_account` block in the VM configuration
- Cannot be assigned through Terraform due to circular dependency

## 🎯 **Current Service Account Roles**

After adding the missing role, the service account should have:

✅ **Already Assigned**:
- Cloud Run developer
- Cloud Scheduler Admin
- Compute Admin
- Compute Storage Admin
- Logging Admin
- Monitoring Admin
- Storage Admin

🔄 **Need to Add Manually**:
- **Service Account User** (`roles/iam.serviceAccountUser`)

## 🚀 **Expected Results**

After making these changes and adding the missing role:

### ✅ **Terraform Should Succeed**:
- No more IAM permission errors
- VM will be created successfully
- Storage bucket will be accessible via Storage Admin role
- All networking resources will be created

### ✅ **Infrastructure Will Work**:
- VM can use the service account
- Full access to required GCP services
- Proper logging and monitoring
- Storage operations will work

## 📋 **Next Steps**

### **IMPORTANT: You Used the Wrong Workflow!**

**Current Issue**: You used `deploy-infrastructure.yml` with `clean-deploy` which creates infrastructure but doesn't destroy it afterward.

**Solution**: Use the correct workflow for your use case:

#### **Option 1: Destroy Current Resources (Recommended)**
1. **Go to GitHub Actions**
2. **Click "Run workflow"** on `Deploy Data Pipeline Infrastructure`
3. **Select**: Environment: `dev`, Action: `destroy`
4. **Click "Run workflow"**

#### **Option 2: Use the Correct Workflow for Pipeline Execution**
After cleanup, use the proper workflow:
1. **Go to GitHub Actions**
2. **Click "Run workflow"** on `Run Data Pipeline`
3. **Select**: Environment: `dev`, Skip VM creation: `false`
4. **Click "Run workflow"**

This workflow will:
- ✅ Create VM
- ✅ Run startup script with SSH connection test
- ✅ Execute data pipeline
- ✅ **Automatically destroy VM and all resources**

### **Manual IAM Step Still Required**
1. **Add the missing IAM role** (Service Account User) in GCP Console
2. **Monitor the deployment** - it should now succeed
3. **Verify VM functionality** after deployment

## 🔒 **Security Benefits**

This approach is actually **more secure** because:
- **Principle of least privilege**: GitHub Actions service account doesn't need IAM admin permissions
- **Manual IAM control**: Critical permissions are managed by humans, not automation
- **Reduced attack surface**: Fewer permissions for the CI/CD pipeline
- **Audit trail**: Manual IAM changes are logged separately
