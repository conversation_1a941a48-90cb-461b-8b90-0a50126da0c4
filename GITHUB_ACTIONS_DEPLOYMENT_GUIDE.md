# GitHub Actions Deployment Guide

## ✅ How Your GitHub Actions Pipeline Works (The Correct Way)

Your pipeline **does NOT** use a committed `terraform.tfvars` file. Instead, it:

1. **Dynamically creates** `terraform.tfvars` from GitHub Secrets during pipeline execution
2. **Uses the file** only during the CI/CD run
3. **Discards the file** after the pipeline completes
4. **Never commits** sensitive data to your repository

This is the **correct and secure approach** for CI/CD pipelines!

## 🔧 What I Fixed

### Issue 1: Multi-line String Format
**Before (Incorrect):**
```yaml
aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
```

**After (Fixed):**
```yaml
aws_private_key = <<-EOT
${{ secrets.AWS_PRIVATE_KEY }}
EOT
```

### Issue 2: Variable Name Mismatch
Fixed `project_repo` → `github_repo` in `main.tf`

## 📋 Step-by-Step Setup Process

### Step 1: Set Up GitHub Repository Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions

Add these **Repository Secrets**:

| Secret Name | Example Value | Description |
|-------------|---------------|-------------|
| `GCP_SA_KEY` | `{"type": "service_account"...}` | GCP Service Account JSON |
| `GCP_PROJECT_ID` | `external-data-source-437915` | Your GCP Project ID |
| `GCP_REGION` | `us-central1` | GCP Region |
| `GCP_ZONE` | `us-central1-a` | GCP Zone |
| `AWS_PRIVATE_KEY` | `-----BEGIN OPENSSH PRIVATE KEY-----\n...` | SSH Private Key |
| `AWS_PUBLIC_KEY` | `ssh-rsa AAAAB3NzaC1yc2E...` | SSH Public Key |
| `AWS_HOSTNAME` | `***********` | AWS EC2 IP Address |
| `AWS_USER` | `forge` | AWS EC2 Username |
| `VM_MACHINE_TYPE` | `e2-standard-4` | (Optional) VM Size |

### Step 2: Format Multi-line Secrets Correctly

For SSH keys, use `\n` to represent newlines in GitHub secrets:

**AWS_PRIVATE_KEY example:**
```
-----BEGIN OPENSSH PRIVATE KEY-----\nb3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAlwAAAAdzc2gtcn\nNhAAAAAwEAAQAAAIEA1234567890abcdef...\n-----END OPENSSH PRIVATE KEY-----
```

**AWS_PUBLIC_KEY example:**
```
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC1234567890abcdef... user@hostname
```

### Step 3: Deploy Using GitHub Actions

#### Option A: Automatic Deployment (Push to main)
```bash
git add .
git commit -m "Deploy infrastructure"
git push origin main
```

#### Option B: Manual Deployment (Workflow Dispatch)
1. Go to GitHub → Actions → "Deploy Data Pipeline Infrastructure"
2. Click "Run workflow"
3. Select:
   - **Environment**: `dev`, `staging`, or `prod`
   - **Action**: `plan`, `apply`, or `destroy`
4. Click "Run workflow"

### Step 4: Monitor the Deployment

1. **Watch the pipeline**: Go to Actions tab to see progress
2. **Check logs**: Click on the running workflow to see detailed logs
3. **Review plan**: For PRs, the plan will be posted as a comment
4. **Get VM info**: After successful deployment, check the logs for VM details

## 🔄 Complete Deployment Process

### What Happens During Deployment:

1. **Checkout code** from your repository
2. **Set up Terraform** and authenticate to GCP
3. **Create GCS bucket** for Terraform state (if needed)
4. **Generate terraform.tfvars** from GitHub secrets using correct heredoc format
5. **Initialize Terraform** with remote state backend
6. **Run terraform plan** to preview changes
7. **Apply changes** (if approved/on main branch)
8. **Output VM information** and startup logs

### Pipeline Triggers:

- **Push to main/develop**: Automatically runs plan and apply
- **Pull Request**: Runs plan only, posts results as comment
- **Manual trigger**: You choose environment and action

## 🚀 How to Deploy Right Now

### For First-Time Setup:
1. **Add all GitHub secrets** (see Step 1 above)
2. **Push your code** to main branch:
   ```bash
   git add .
   git commit -m "Initial infrastructure setup"
   git push origin main
   ```
3. **Monitor the pipeline** in GitHub Actions

### For Manual Deployment:
1. Go to **GitHub → Actions**
2. Select **"Deploy Data Pipeline Infrastructure"**
3. Click **"Run workflow"**
4. Choose **environment** and **action**
5. Click **"Run workflow"**

## 🔍 Troubleshooting

### If you see "Invalid multi-line string" errors:
- ✅ **Fixed**: The workflow now uses correct heredoc format

### If you see "vars map does not contain key" errors:
- ✅ **Fixed**: Variable name mismatch resolved

### If secrets are missing:
- Check that all required secrets are set in GitHub repository settings
- Verify secret names match exactly (case-sensitive)

### If GCP authentication fails:
- Ensure `GCP_SA_KEY` contains valid service account JSON
- Verify the service account has required permissions

## 📝 Key Points

- ✅ **No terraform.tfvars file needed** in your repository
- ✅ **All sensitive data** stored securely in GitHub Secrets
- ✅ **Automatic state management** with GCS backend
- ✅ **Environment-specific deployments** (dev/staging/prod)
- ✅ **Plan preview** for pull requests
- ✅ **Manual control** via workflow dispatch

Your setup is now ready for secure, automated deployments! 🎉
